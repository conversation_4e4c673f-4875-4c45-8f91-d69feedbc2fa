<idea-plugin package="cc.unitmesh.idea">
    <!--suppress PluginXmlValidity -->
    <dependencies>
        <plugin id="com.intellij.modules.java"/>
        <plugin id="org.jetbrains.plugins.gradle"/>
        <plugin id="org.jetbrains.idea.maven"/>
    </dependencies>

    <extensions defaultExtensionNs="cc.unitmesh">
        <classContextBuilder language="JAVA"
                             implementationClass="cc.unitmesh.idea.context.JavaClassContextBuilder"/>

        <methodContextBuilder language="JAVA"
                              implementationClass="cc.unitmesh.idea.context.JavaMethodContextBuilder"/>

        <fileContextBuilder language="JAVA"
                            implementationClass="cc.unitmesh.idea.context.JavaFileContextBuilder"/>

        <variableContextBuilder language="JAVA"
                                implementationClass="cc.unitmesh.idea.context.JavaVariableContextBuilder"/>

        <codeModifier language="JAVA"
                      implementationClass="cc.unitmesh.idea.context.JavaCodeModifier"/>

        <livingDocumentation language="JAVA"
                             implementationClass="cc.unitmesh.idea.provider.JavaLivingDocumentation"/>

        <testDataBuilder language="JAVA"
                         implementationClass="cc.unitmesh.idea.provider.JavaPsiElementDataBuilder"/>

        <refactoringTool language="JAVA"
                         implementationClass="cc.unitmesh.idea.provider.JavaRefactoringTool"/>

        <chatContextProvider implementation="cc.unitmesh.idea.provider.JavaVersionProvider"/>
        <chatContextProvider implementation="cc.unitmesh.idea.provider.SpringGradleContextProvider"/>
        <chatContextProvider implementation="cc.unitmesh.idea.provider.JavaTestContextProvider"/>

        <contextPrompter
                language="JAVA"
                implementation="cc.unitmesh.idea.prompting.JavaContextPrompter"/>

        <testContextProvider
                language="JAVA"
                implementation="cc.unitmesh.idea.service.JavaAutoTestService"/>

        <buildSystemProvider
                implementation="cc.unitmesh.idea.provider.JavaBuildSystemProvider"/>

        <customPromptProvider
                language="JAVA"
                implementationClass="cc.unitmesh.idea.provider.JavaCustomPromptProvider"/>

        <relatedClassProvider
                language="JAVA"
                implementationClass="cc.unitmesh.idea.provider.JavaRelatedClassesProvider" />

        <customDevInsCompletionProvider
                implementation="cc.unitmesh.idea.provider.JavaCustomDevInsSymbolProvider"/>

        <runProjectService implementation="cc.unitmesh.idea.provider.JvmRunProjectService"/>
<!--        <agentObserver implementation="cc.unitmesh.idea.observer.GradleTaskAgentObserver" />-->

        <frameworkConfigProvider implementation="cc.unitmesh.idea.provider.SpringFrameworkConfigProvider"/>

        <langDictProvider implementation="cc.unitmesh.idea.indexer.provider.JavaLangDictProvider"/>

        <shirePsiQLInterpreter language="JAVA"
                               implementationClass="cc.unitmesh.idea.provider.JavaShireQLInterpreter"/>
        <shireSymbolProvider implementation="cc.unitmesh.idea.provider.JavaSymbolProvider"/>
    </extensions>
</idea-plugin>
