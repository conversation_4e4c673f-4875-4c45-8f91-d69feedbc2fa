{"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"mcpServers": {"type": "object", "additionalProperties": {"type": "object", "properties": {"url": {"type": "string"}, "command": {"type": "string"}, "args": {"type": "array", "items": {"type": "string"}}, "disabled": {"type": "boolean"}, "autoApprove": {"type": "array", "items": {"type": "string"}}, "env": {"type": "object", "additionalProperties": {"type": "string"}}, "requires_confirmation": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}}}, "required": ["mcpServers"]}