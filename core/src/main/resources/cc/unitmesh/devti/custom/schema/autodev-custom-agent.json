{"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "auth": {"type": "object", "properties": {"type": {"type": "string", "enum": ["Bearer"]}, "token": {"type": "string"}}, "required": ["type", "token"]}, "connector": {"type": "object", "properties": {"requestFormat": {"type": "string", "description": "JSONPath expression to specify the format of the request"}, "responseFormat": {"type": "string", "description": "JSONPath expression to specify the format of the request"}}}, "responseAction": {"type": "string", "enum": ["Direct", "TextChunk", "WebView", "DevIns", "Stream", "Flow"]}, "defaultTimeout": {"type": "number"}, "enabled": {"type": "boolean"}}, "required": ["name", "url", "description", "responseAction"], "additionalProperties": true}}