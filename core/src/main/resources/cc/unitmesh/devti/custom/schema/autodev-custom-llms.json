{"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "url": {"type": "string", "format": "uri"}, "auth": {"type": "object", "properties": {"type": {"type": "string"}, "token": {"type": "string", "format": "password"}}, "required": ["token"]}, "requestFormat": {"type": "string", "x-intellij-language-injection": "JSON"}, "responseFormat": {"type": "string", "x-intellij-language-injection": "JSONPath"}, "modelType": {"type": "string", "enum": ["<PERSON><PERSON><PERSON>", "Plan", "Act", "Completion", "Embedding", "FastApply", "Others"], "default": "Others"}}, "required": ["name", "url", "auth", "requestFormat", "responseFormat"]}}