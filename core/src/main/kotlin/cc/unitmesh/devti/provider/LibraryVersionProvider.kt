package cc.unitmesh.devti.provider

import cc.unitmesh.devti.util.LazyExtensionInstance
import com.intellij.openapi.extensions.ExtensionPointName
import com.intellij.openapi.project.Project
import kotlinx.serialization.Serializable

/**
 * Abstract provider for fetching library version information from different package managers.
 * Implementations should handle specific package manager APIs (npm, maven, pypi, etc.).
 */
abstract class LibraryVersionProvider : LazyExtensionInstance<LibraryVersionProvider>() {
    
    /**
     * The type of package manager this provider handles (e.g., "npm", "maven", "pypi")
     */
    abstract val packageType: String
    
    /**
     * Check if this provider can handle the given package type
     */
    open fun canHandle(packageType: String): Bo<PERSON>an {
        return this.packageType.equals(packageType, ignoreCase = true)
    }
    
    /**
     * Fetch version information for a single package
     */
    abstract suspend fun fetchVersion(request: VersionRequest): VersionResult
    
    /**
     * Fetch version information for multiple packages
     */
    open suspend fun fetchVersions(requests: List<VersionRequest>): List<VersionResult> {
        return requests.map { fetchVersion(it) }
    }
    
    /**
     * Get supported package types by this provider
     */
    open fun getSupportedTypes(): List<String> {
        return listOf(packageType)
    }
    
    companion object {
        val EP_NAME: ExtensionPointName<LibraryVersionProvider> =
            ExtensionPointName.create("cc.unitmesh.libraryVersionProvider")
        
        /**
         * Find provider for the given package type
         */
        fun findProvider(packageType: String): LibraryVersionProvider? {
            return EP_NAME.extensionList.find { it.canHandle(packageType) }
        }
        
        /**
         * Get all available providers
         */
        fun getAllProviders(): List<LibraryVersionProvider> {
            return EP_NAME.extensionList
        }
        
        /**
         * Get all supported package types
         */
        fun getSupportedTypes(): List<String> {
            return EP_NAME.extensionList.flatMap { it.getSupportedTypes() }.distinct()
        }
    }
}

/**
 * Request for fetching library version information
 */
@Serializable
data class VersionRequest(
    val name: String,
    val type: String,
    val timeout: Int = 5000,
    val includePrerelease: Boolean = false,
    val registry: String? = null
)

/**
 * Result of version fetching operation
 */
@Serializable
data class VersionResult(
    val name: String,
    val type: String,
    val version: String? = null,
    val error: String? = null,
    val success: Boolean = version != null,
    val metadata: Map<String, String> = emptyMap()
) {
    companion object {
        fun success(name: String, type: String, version: String, metadata: Map<String, String> = emptyMap()): VersionResult {
            return VersionResult(name, type, version, null, true, metadata)
        }
        
        fun error(name: String, type: String, error: String): VersionResult {
            return VersionResult(name, type, null, error, false)
        }
    }
}

/**
 * Configuration for library version fetching
 */
@Serializable
data class LibraryVersionConfig(
    val packages: List<VersionRequest> = emptyList(),
    val options: VersionOptions = VersionOptions()
) {
    // Support single package format
    val name: String? = null
    val type: String? = null
    val timeout: Int? = null
    
    /**
     * Convert to list of requests, handling both single and multiple package formats
     */
    fun toRequests(): List<VersionRequest> {
        return if (packages.isNotEmpty()) {
            packages
        } else if (name != null && type != null) {
            listOf(VersionRequest(name, type, timeout ?: options.timeout))
        } else {
            emptyList()
        }
    }
}

/**
 * Options for version fetching
 */
@Serializable
data class VersionOptions(
    val timeout: Int = 5000,
    val autoDetect: Boolean = true,
    val includePrerelease: Boolean = false,
    val maxConcurrent: Int = 5
)
